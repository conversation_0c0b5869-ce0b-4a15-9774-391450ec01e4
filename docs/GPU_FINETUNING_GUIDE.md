# 🚀 GPU & Hyperparameter Optimization Fine-tuning Guide

## 📍 Configuration File Location
**Primary Config**: `config/strategy_evolution_config.yaml` (lines 486-532)

---

## 🔧 GPU Worker Configuration

### **parallel_workers** (Most Important Setting)
```yaml
parallel_workers: 6  # Active parallel workers (1-16)
```

**What it does**: Controls how many GPU workers process tasks simultaneously
**Impact**:
- **Increase (6→8)**: ✅ Higher throughput, faster processing
- **Increase (8→12)**: ⚠️ May cause GPU memory contention, slower per-task
- **Decrease (6→4)**: ✅ More stable, less memory usage
- **Decrease (4→2)**: ✅ Very stable, but slower overall

**Recommended Values**:
- **RTX 3060 Ti (8GB)**: 4-6 workers (optimal)
- **RTX 4090 (24GB)**: 8-12 workers
- **RTX 3080 (10GB)**: 6-8 workers
- **GTX 1660 (6GB)**: 2-4 workers

---

### **max_workers** (Upper Limit)
```yaml
max_workers: 8  # Maximum number of workers (1-16)
```

**What it does**: Sets the absolute maximum workers allowed
**Impact**:
- **Must be ≥ parallel_workers**
- **Higher values**: Allow more flexibility but use more system resources
- **Lower values**: Provide safety limits

---

### **max_concurrent_tasks** (Task Queue Size)
```yaml
max_concurrent_tasks: 40  # Max tasks in queue (10-200)
```

**What it does**: How many tasks can be queued for processing
**Impact**:
- **Increase (40→80)**: ✅ Better task batching, higher throughput
- **Increase (80→200)**: ⚠️ Higher memory usage, potential bottlenecks
- **Decrease (40→20)**: ✅ Lower memory usage, more responsive
- **Decrease (20→10)**: ✅ Very conservative, may underutilize GPU

---

## 💾 Memory Configuration

### **pool_size_gb** (GPU Memory Pool)
```yaml
pool_size_gb: 6.0  # Memory pool size in GB (2.0-7.5)
```

**What it does**: Reserves GPU memory for processing
**Impact**:
- **Increase (6.0→7.5)**: ✅ Handle larger datasets, fewer memory errors
- **Increase (7.5→8.0)**: ❌ May cause out-of-memory errors
- **Decrease (6.0→4.0)**: ✅ More stable, leaves room for other processes
- **Decrease (4.0→2.0)**: ⚠️ May limit processing capability

**GPU-Specific Recommendations**:
- **RTX 3060 Ti (8GB)**: 6.0-7.0GB
- **RTX 4090 (24GB)**: 18.0-20.0GB
- **RTX 3080 (10GB)**: 7.0-8.5GB

---

### **batch_size** (Processing Batch Size)
```yaml
batch_size: 1024  # Batch size for GPU processing (256-8192)
```

**What it does**: How many data points processed together
**Impact**:
- **Increase (1024→2048)**: ✅ Better GPU utilization, faster processing
- **Increase (2048→4096)**: ✅ Maximum throughput (if memory allows)
- **Increase (4096→8192)**: ⚠️ May cause memory errors
- **Decrease (1024→512)**: ✅ More stable, lower memory usage

---

## ⚡ Performance Tuning

### **optimization_trials** (Hyperparameter Optimization)
```yaml
optimization_trials: 50  # Number of optimization trials (10-500)
```

**What it does**: How many parameter combinations to test per strategy
**Impact**:
- **Increase (50→100)**: ✅ Better optimization, higher quality strategies
- **Increase (100→200)**: ✅ Excellent optimization, longer processing time
- **Increase (200→500)**: ⚠️ Diminishing returns, very long processing
- **Decrease (50→25)**: ✅ Faster processing, potentially lower quality
- **Decrease (25→10)**: ⚠️ Very fast but poor optimization

---

### **parallel_optimization_jobs** (Optimization Parallelism)
```yaml
parallel_optimization_jobs: 4  # Parallel optimization jobs (1-8)
```

**What it does**: How many optimization processes run simultaneously
**Impact**:
- **Increase (4→6)**: ✅ Faster optimization, higher CPU usage
- **Increase (6→8)**: ⚠️ May overwhelm system resources
- **Decrease (4→2)**: ✅ More stable, lower resource usage

---

## 🎯 Free Optuna Alternatives Used

### **Primary Optimizers** (No Cost!)
1. **Scikit-Optimize (skopt)**: Bayesian optimization
2. **Hyperopt**: Tree-structured Parzen Estimator
3. **Ray Tune**: Distributed hyperparameter tuning
4. **Optuna-free**: Custom implementation
5. **GridSearchCV**: Exhaustive search
6. **RandomizedSearchCV**: Random sampling

### **Auto-Selection Logic**
```python
# System automatically chooses best optimizer based on:
if n_trials < 20:
    use_random_search()  # Fast for small searches
elif n_trials < 100:
    use_hyperopt()       # Good balance
else:
    use_ray_tune()       # Best for large searches
```

---

## 📊 Monitoring & Diagnostics

### **Performance Monitoring**
```yaml
enable_monitoring: true          # Enable GPU performance monitoring
monitoring_interval: 10         # Monitor every N seconds (5-60)
log_performance_stats: true     # Log detailed performance statistics
```

### **Key Metrics to Watch**
- **GPU Utilization**: Should be >80% during processing
- **Memory Usage**: Should stay below pool_size_gb
- **Task Completion Rate**: Higher = better throughput
- **Error Rate**: Should be <1%

---

## ⚙️ Recommended Configurations

### **🏎️ High Performance** (RTX 3060 Ti)
```yaml
parallel_workers: 6
max_concurrent_tasks: 80
batch_size: 2048
pool_size_gb: 7.0
optimization_trials: 100
```

### **🛡️ Stable/Conservative**
```yaml
parallel_workers: 4
max_concurrent_tasks: 40
batch_size: 1024
pool_size_gb: 6.0
optimization_trials: 50
```

### **⚡ Maximum Speed** (Risk of instability)
```yaml
parallel_workers: 8
max_concurrent_tasks: 120
batch_size: 4096
pool_size_gb: 7.5
optimization_trials: 200
```

---

## 🚨 Warning Signs & Troubleshooting

### **Memory Issues**
- **Symptom**: "CUDA out of memory" errors
- **Fix**: Reduce `pool_size_gb`, `batch_size`, or `parallel_workers`

### **Slow Performance**
- **Symptom**: GPU utilization <50%
- **Fix**: Increase `parallel_workers`, `batch_size`, or `max_concurrent_tasks`

### **System Instability**
- **Symptom**: System freezes, driver crashes
- **Fix**: Reduce `parallel_workers` and `pool_size_gb`

### **Poor Optimization Results**
- **Symptom**: Low strategy quality scores
- **Fix**: Increase `optimization_trials` and `parallel_optimization_jobs`

---

## 🎯 Quick Start Recommendations

1. **Start Conservative**: Use stable configuration first
2. **Monitor Performance**: Watch GPU utilization and memory
3. **Gradually Increase**: Bump up workers/batch size slowly
4. **Test Thoroughly**: Run multiple cycles to ensure stability
5. **Document Changes**: Keep track of what works best

**Next Steps**: See `GPU_PARALLEL_PROCESSING_IMPROVEMENTS.md` for advanced techniques.
