#!/usr/bin/env python3
"""
Advanced fix for strategies.yaml by reconstructing clean YAML
"""

import yaml
import re
from pathlib import Path

def extract_strategy_data(content):
    """Extract strategy data from corrupted YAML"""
    strategies = []
    
    # Split by strategy entries
    strategy_blocks = re.split(r'^- best_risk_reward:', content, flags=re.MULTILINE)
    
    for i, block in enumerate(strategy_blocks[1:], 1):  # Skip first empty block
        try:
            # Extract basic fields with regex
            strategy = {}
            
            # Extract best_risk_reward (first line)
            risk_reward_match = re.search(r'^([0-9.]+)', block.strip())
            if risk_reward_match:
                strategy['best_risk_reward'] = float(risk_reward_match.group(1))
            else:
                strategy['best_risk_reward'] = 1.0
            
            # Extract name
            name_match = re.search(r'name:\s*(.+)', block)
            if name_match:
                strategy['name'] = name_match.group(1).strip()
            else:
                strategy['name'] = f'Strategy_{i}'
            
            # Extract ranking
            ranking_match = re.search(r'ranking:\s*(\d+)', block)
            if ranking_match:
                strategy['ranking'] = int(ranking_match.group(1))
            else:
                strategy['ranking'] = 0
            
            # Extract performance summary fields with defaults
            perf_fields = {
                'max_drawdown': 20.0,
                'sharpe_ratio': 0.0,
                'total_return': 0.0,
                'win_rate': 0.5,
                'profit_factor': 1.0
            }
            
            strategy['performance_summary'] = {}
            for field, default in perf_fields.items():
                field_match = re.search(rf'{field}:\s*([0-9.-]+)', block)
                if field_match:
                    strategy['performance_summary'][field] = float(field_match.group(1))
                else:
                    strategy['performance_summary'][field] = default
            
            # Extract parameters if they exist
            params_match = re.search(r'parameters:\s*\n((?:\s+.+\n)*)', block)
            if params_match:
                params_text = params_match.group(1)
                strategy['parameters'] = {}
                
                # Extract parameter values
                param_matches = re.findall(r'\s+(\w+):\s*([0-9.-]+)', params_text)
                for param_name, param_value in param_matches:
                    try:
                        strategy['parameters'][param_name] = float(param_value)
                    except:
                        strategy['parameters'][param_name] = param_value
            
            strategies.append(strategy)
            
        except Exception as e:
            print(f"⚠️ Error processing strategy block {i}: {e}")
            continue
    
    return strategies

def fix_strategies_yaml_v2():
    """Advanced fix for strategies.yaml"""
    
    strategies_path = Path("config/strategies.yaml")
    backup_path = Path("config/strategies_backup_v2.yaml")
    
    print("🔧 Advanced fix for strategies.yaml...")
    
    # Create backup
    if strategies_path.exists():
        import shutil
        shutil.copy2(strategies_path, backup_path)
        print(f"✅ Backup created: {backup_path}")
    
    # Read the raw file content
    with open(strategies_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"📊 Original file size: {len(content)} characters")
    
    # Extract strategy data
    strategies = extract_strategy_data(content)
    print(f"📊 Extracted {len(strategies)} strategies")
    
    # Create clean YAML structure
    clean_data = {
        'strategies': strategies
    }
    
    # Write clean YAML
    with open(strategies_path, 'w', encoding='utf-8') as f:
        yaml.dump(clean_data, f, default_flow_style=False, sort_keys=False, indent=2)
    
    print(f"✅ Clean YAML written")
    
    # Test if the YAML can be loaded
    try:
        with open(strategies_path, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
        
        strategies = data.get('strategies', [])
        print(f"✅ YAML file fixed successfully!")
        print(f"📊 Loaded {len(strategies)} strategies")
        
        # Count strategies by ranking
        ranking_counts = {}
        for strategy in strategies:
            ranking = strategy.get('ranking', 0)
            ranking_counts[ranking] = ranking_counts.get(ranking, 0) + 1
        
        print("📈 Strategy distribution by ranking:")
        for ranking in sorted(ranking_counts.keys()):
            print(f"   Ranking {ranking}: {ranking_counts[ranking]} strategies")
        
        # Show sample strategy
        if strategies:
            print("\n📋 Sample strategy:")
            sample = strategies[0]
            print(f"   Name: {sample.get('name', 'N/A')}")
            print(f"   Ranking: {sample.get('ranking', 'N/A')}")
            print(f"   Risk/Reward: {sample.get('best_risk_reward', 'N/A')}")
            print(f"   Sharpe Ratio: {sample.get('performance_summary', {}).get('sharpe_ratio', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ YAML file still has issues: {e}")
        
        # Restore backup
        if backup_path.exists():
            import shutil
            shutil.copy2(backup_path, strategies_path)
            print("🔄 Restored from backup")
        
        return False

if __name__ == "__main__":
    success = fix_strategies_yaml_v2()
    if success:
        print("🎉 strategies.yaml fixed successfully!")
    else:
        print("❌ Failed to fix strategies.yaml")
