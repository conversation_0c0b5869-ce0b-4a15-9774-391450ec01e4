#!/usr/bin/env python3
"""
GPU Parallel Processing Comparison Script
Demonstrates the performance difference between sequential and parallel GPU processing
"""

import asyncio
import time
import numpy as np
import torch
import cupy as cp
from datetime import datetime
from typing import List, Dict, Any
import logging
from pathlib import Path
import polars as pl

# Configure logging with timestamps
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s.%(msecs)03d - %(name)s - %(levelname)s - %(message)s',
    datefmt='%H:%M:%S'
)
logger = logging.getLogger(__name__)

def timestamp() -> str:
    """Get formatted timestamp"""
    return datetime.now().strftime('%H:%M:%S.%f')[:-3]

class PerformanceComparison:
    """Compare sequential vs parallel GPU processing performance"""
    
    def __init__(self):
        self.cuda_available = torch.cuda.is_available()
        self.device_count = torch.cuda.device_count() if self.cuda_available else 0
        
        if self.cuda_available:
            print(f"[{timestamp()}] 🚀 GPU Performance Comparison initialized")
            print(f"[{timestamp()}] 📊 Available GPUs: {self.device_count}")
            for i in range(self.device_count):
                gpu_name = torch.cuda.get_device_name(i)
                gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                print(f"[{timestamp()}] 🎯 GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)")
        else:
            print(f"[{timestamp()}] ⚠️ CUDA not available - comparison will use CPU")
    
    def generate_test_data(self, n_stocks: int = 10, n_rows: int = 10000) -> List[Dict[str, np.ndarray]]:
        """Generate test data for multiple stocks"""
        print(f"[{timestamp()}] 📊 Generating test data for {n_stocks} stocks with {n_rows} rows each")
        
        test_data = []
        for i in range(n_stocks):
            # Generate realistic stock data
            base_price = 100 + np.random.random() * 900  # Price between 100-1000
            returns = np.random.normal(0, 0.02, n_rows)  # 2% daily volatility
            prices = base_price * np.exp(np.cumsum(returns))
            
            # Add some noise for high/low
            high_prices = prices * (1 + np.abs(np.random.normal(0, 0.01, n_rows)))
            low_prices = prices * (1 - np.abs(np.random.normal(0, 0.01, n_rows)))
            
            # Generate volume
            volume = np.random.lognormal(10, 1, n_rows).astype(int)
            
            stock_data = {
                'close': prices.astype(np.float32),
                'high': high_prices.astype(np.float32),
                'low': low_prices.astype(np.float32),
                'volume': volume.astype(np.float32),
                'stock_name': f'STOCK_{i:03d}'
            }
            test_data.append(stock_data)
        
        print(f"[{timestamp()}] ✅ Test data generation completed")
        return test_data
    
    def generate_test_strategies(self, n_strategies: int = 5) -> List[Dict[str, Any]]:
        """Generate test strategies"""
        strategies = []
        strategy_types = ['RSI_Reversal', 'EMA_Crossover', 'Momentum', 'Bollinger_Bands', 'MACD']
        
        for i in range(n_strategies):
            strategy = {
                'name': f'{strategy_types[i % len(strategy_types)]}_{i}',
                'type': strategy_types[i % len(strategy_types)],
                'parameters': {
                    'period': 14 + (i * 2),
                    'threshold': 0.02 + (i * 0.005)
                }
            }
            strategies.append(strategy)
        
        return strategies
    
    async def run_sequential_processing(self, test_data: List[Dict[str, np.ndarray]], 
                                      strategies: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Run sequential GPU processing (old method)"""
        print(f"[{timestamp()}] 🐌 Starting SEQUENTIAL GPU processing...")
        start_time = time.time()
        
        results = {}
        total_operations = 0
        
        for i, stock_data in enumerate(test_data):
            stock_name = stock_data['stock_name']
            print(f"[{timestamp()}] 📈 Processing {stock_name} ({i+1}/{len(test_data)}) - SEQUENTIAL")
            
            for j, strategy in enumerate(strategies):
                strategy_name = f"{strategy['name']}_{stock_name}"
                
                # Simulate sequential GPU processing
                if self.cuda_available:
                    # Convert to GPU
                    gpu_data = {
                        'close': cp.asarray(stock_data['close']),
                        'high': cp.asarray(stock_data['high']),
                        'low': cp.asarray(stock_data['low']),
                        'volume': cp.asarray(stock_data['volume'])
                    }
                    
                    # Process one strategy at a time (sequential)
                    signals = self._process_single_strategy_gpu(gpu_data, strategy)\n                    \n                    # Convert back to CPU\n                    results[strategy_name] = cp.asnumpy(signals)\n                    \n                    # Clean up GPU memory after each operation (inefficient)\n                    cp.get_default_memory_pool().free_all_blocks()\n                else:\n                    # CPU fallback\n                    signals = self._process_single_strategy_cpu(stock_data, strategy)\n                    results[strategy_name] = signals\n                \n                total_operations += 1\n                \n                # Add small delay to simulate real processing\n                await asyncio.sleep(0.01)\n        \n        processing_time = time.time() - start_time\n        \n        print(f\"[{timestamp()}] 🐌 SEQUENTIAL processing completed in {processing_time:.3f}s\")\n        print(f\"[{timestamp()}] 📊 Processed {total_operations} operations sequentially\")\n        print(f\"[{timestamp()}] ⏱️ Average time per operation: {processing_time/total_operations:.4f}s\")\n        \n        return {\n            'method': 'sequential',\n            'total_time': processing_time,\n            'total_operations': total_operations,\n            'avg_time_per_operation': processing_time / total_operations,\n            'results': results\n        }\n    \n    async def run_parallel_processing(self, test_data: List[Dict[str, np.ndarray]], \n                                    strategies: List[Dict[str, Any]]) -> Dict[str, Any]:\n        \"\"\"Run parallel GPU processing (new method)\"\"\"\n        print(f\"[{timestamp()}] ⚡ Starting PARALLEL GPU processing...\")\n        start_time = time.time()\n        \n        results = {}\n        total_operations = len(test_data) * len(strategies)\n        \n        if self.cuda_available:\n            # Use multiple CUDA streams for true parallelism\n            n_streams = min(8, self.device_count * 2)\n            streams = [cp.cuda.Stream() for _ in range(n_streams)]\n            \n            print(f\"[{timestamp()}] 🚀 Using {n_streams} CUDA streams for parallel processing\")\n            \n            # Process all combinations in parallel batches\n            batch_size = n_streams\n            all_tasks = []\n            \n            # Create all tasks\n            for stock_data in test_data:\n                for strategy in strategies:\n                    all_tasks.append((stock_data, strategy))\n            \n            # Process in parallel batches\n            for i in range(0, len(all_tasks), batch_size):\n                batch_tasks = all_tasks[i:i + batch_size]\n                batch_results = await self._process_batch_parallel_gpu(batch_tasks, streams)\n                results.update(batch_results)\n                \n                print(f\"[{timestamp()}] ⚡ Processed batch {i//batch_size + 1}/{(len(all_tasks) + batch_size - 1)//batch_size}\")\n            \n            # Clean up streams\n            for stream in streams:\n                stream.synchronize()\n            \n            # Final cleanup\n            cp.get_default_memory_pool().free_all_blocks()\n            torch.cuda.empty_cache()\n        else:\n            # CPU parallel processing using asyncio\n            tasks = []\n            for stock_data in test_data:\n                for strategy in strategies:\n                    task = self._process_single_strategy_cpu_async(stock_data, strategy)\n                    tasks.append(task)\n            \n            # Process all tasks in parallel\n            task_results = await asyncio.gather(*tasks)\n            \n            # Collect results\n            task_idx = 0\n            for stock_data in test_data:\n                for strategy in strategies:\n                    strategy_name = f\"{strategy['name']}_{stock_data['stock_name']}\"\n                    results[strategy_name] = task_results[task_idx]\n                    task_idx += 1\n        \n        processing_time = time.time() - start_time\n        \n        print(f\"[{timestamp()}] ⚡ PARALLEL processing completed in {processing_time:.3f}s\")\n        print(f\"[{timestamp()}] 📊 Processed {total_operations} operations in parallel\")\n        print(f\"[{timestamp()}] ⏱️ Average time per operation: {processing_time/total_operations:.4f}s\")\n        \n        return {\n            'method': 'parallel',\n            'total_time': processing_time,\n            'total_operations': total_operations,\n            'avg_time_per_operation': processing_time / total_operations,\n            'results': results\n        }\n    \n    async def _process_batch_parallel_gpu(self, batch_tasks: List[tuple], streams: List) -> Dict[str, np.ndarray]:\n        \"\"\"Process a batch of tasks in parallel using multiple GPU streams\"\"\"\n        batch_results = {}\n        \n        # Process tasks in parallel using different streams\n        for i, (stock_data, strategy) in enumerate(batch_tasks):\n            stream_idx = i % len(streams)\n            stream = streams[stream_idx]\n            \n            stock_name = stock_data['stock_name']\n            strategy_name = f\"{strategy['name']}_{stock_name}\"\n            \n            # Convert to GPU with specific stream\n            with stream:\n                gpu_data = {\n                    'close': cp.asarray(stock_data['close']),\n                    'high': cp.asarray(stock_data['high']),\n                    'low': cp.asarray(stock_data['low']),\n                    'volume': cp.asarray(stock_data['volume'])\n                }\n                \n                # Process with stream\n                signals = self._process_single_strategy_gpu(gpu_data, strategy, stream)\n                \n                # Convert back to CPU\n                batch_results[strategy_name] = cp.asnumpy(signals)\n        \n        # Synchronize all streams\n        for stream in streams:\n            stream.synchronize()\n        \n        return batch_results\n    \n    def _process_single_strategy_gpu(self, gpu_data: Dict[str, cp.ndarray], \n                                   strategy: Dict[str, Any], \n                                   stream=None) -> cp.ndarray:\n        \"\"\"Process single strategy on GPU\"\"\"\n        close = gpu_data['close']\n        n_rows = len(close)\n        signals = cp.zeros(n_rows, dtype=cp.int8)\n        \n        strategy_type = strategy['type']\n        \n        if strategy_type == 'RSI_Reversal':\n            # Simple RSI calculation\n            period = strategy['parameters'].get('period', 14)\n            for i in range(period, n_rows):\n                # Simplified RSI calculation\n                gains = cp.sum(cp.maximum(cp.diff(close[i-period:i]), 0))\n                losses = cp.sum(cp.maximum(-cp.diff(close[i-period:i]), 0))\n                \n                if losses > 0:\n                    rs = gains / losses\n                    rsi = 100 - (100 / (1 + rs))\n                    \n                    if rsi < 30:\n                        signals[i] = 1  # Buy\n                    elif rsi > 70:\n                        signals[i] = -1  # Sell\n        \n        elif strategy_type == 'EMA_Crossover':\n            # Simple EMA crossover\n            fast_period = 10\n            slow_period = 20\n            \n            # Calculate EMAs (simplified)\n            alpha_fast = 2.0 / (fast_period + 1)\n            alpha_slow = 2.0 / (slow_period + 1)\n            \n            ema_fast = cp.zeros_like(close)\n            ema_slow = cp.zeros_like(close)\n            \n            ema_fast[0] = close[0]\n            ema_slow[0] = close[0]\n            \n            for i in range(1, n_rows):\n                ema_fast[i] = alpha_fast * close[i] + (1 - alpha_fast) * ema_fast[i-1]\n                ema_slow[i] = alpha_slow * close[i] + (1 - alpha_slow) * ema_slow[i-1]\n                \n                if i > slow_period:\n                    if ema_fast[i] > ema_slow[i] and ema_fast[i-1] <= ema_slow[i-1]:\n                        signals[i] = 1  # Buy\n                    elif ema_fast[i] < ema_slow[i] and ema_fast[i-1] >= ema_slow[i-1]:\n                        signals[i] = -1  # Sell\n        \n        else:\n            # Default momentum strategy\n            lookback = strategy['parameters'].get('period', 10)\n            threshold = strategy['parameters'].get('threshold', 0.02)\n            \n            for i in range(lookback, n_rows):\n                momentum = (close[i] - close[i - lookback]) / close[i - lookback]\n                if momentum > threshold:\n                    signals[i] = 1\n                elif momentum < -threshold:\n                    signals[i] = -1\n        \n        return signals\n    \n    def _process_single_strategy_cpu(self, stock_data: Dict[str, np.ndarray], \n                                   strategy: Dict[str, Any]) -> np.ndarray:\n        \"\"\"Process single strategy on CPU\"\"\"\n        close = stock_data['close']\n        n_rows = len(close)\n        signals = np.zeros(n_rows, dtype=np.int8)\n        \n        # Simple signal generation (placeholder)\n        for i in range(20, n_rows, 50):  # Generate signals every 50 bars\n            if np.random.random() > 0.5:\n                signals[i] = 1 if np.random.random() > 0.5 else -1\n        \n        return signals\n    \n    async def _process_single_strategy_cpu_async(self, stock_data: Dict[str, np.ndarray], \n                                               strategy: Dict[str, Any]) -> np.ndarray:\n        \"\"\"Async CPU processing\"\"\"\n        # Add small delay to simulate processing\n        await asyncio.sleep(0.001)\n        return self._process_single_strategy_cpu(stock_data, strategy)\n    \n    def compare_results(self, sequential_results: Dict[str, Any], \n                       parallel_results: Dict[str, Any]):\n        \"\"\"Compare sequential vs parallel results\"\"\"\n        print(f\"\\n[{timestamp()}] 📊 PERFORMANCE COMPARISON RESULTS\")\n        print(\"=\" * 80)\n        \n        seq_time = sequential_results['total_time']\n        par_time = parallel_results['total_time']\n        speedup = seq_time / par_time if par_time > 0 else 0\n        \n        print(f\"🐌 Sequential Processing:\")\n        print(f\"   Total Time: {seq_time:.3f} seconds\")\n        print(f\"   Operations: {sequential_results['total_operations']}\")\n        print(f\"   Avg Time/Op: {sequential_results['avg_time_per_operation']:.4f} seconds\")\n        \n        print(f\"\\n⚡ Parallel Processing:\")\n        print(f\"   Total Time: {par_time:.3f} seconds\")\n        print(f\"   Operations: {parallel_results['total_operations']}\")\n        print(f\"   Avg Time/Op: {parallel_results['avg_time_per_operation']:.4f} seconds\")\n        \n        print(f\"\\n🚀 Performance Improvement:\")\n        print(f\"   Speedup: {speedup:.2f}x faster\")\n        print(f\"   Time Saved: {seq_time - par_time:.3f} seconds ({((seq_time - par_time) / seq_time * 100):.1f}%)\")\n        \n        if speedup > 1:\n            print(f\"   ✅ Parallel processing is {speedup:.2f}x faster!\")\n        else:\n            print(f\"   ⚠️ Parallel processing is slower (overhead may be too high for small datasets)\")\n        \n        print(\"=\" * 80)\n        \n        # Log results\n        logger.info(f\"Performance comparison: Sequential={seq_time:.3f}s, Parallel={par_time:.3f}s, Speedup={speedup:.2f}x\")\n\nasync def main():\n    \"\"\"Main comparison function\"\"\"\n    print(f\"[{timestamp()}] 🚀 Starting GPU Parallel Processing Comparison\")\n    \n    # Initialize comparison\n    comparison = PerformanceComparison()\n    \n    # Generate test data\n    n_stocks = 8  # Moderate number for realistic comparison\n    n_strategies = 4\n    n_rows = 5000  # Realistic data size\n    \n    test_data = comparison.generate_test_data(n_stocks, n_rows)\n    strategies = comparison.generate_test_strategies(n_strategies)\n    \n    print(f\"[{timestamp()}] 📋 Test Configuration:\")\n    print(f\"   Stocks: {n_stocks}\")\n    print(f\"   Strategies: {n_strategies}\")\n    print(f\"   Data Points per Stock: {n_rows}\")\n    print(f\"   Total Operations: {n_stocks * n_strategies}\")\n    \n    # Run sequential processing\n    print(f\"\\n[{timestamp()}] 🐌 Running Sequential Processing Test...\")\n    sequential_results = await comparison.run_sequential_processing(test_data, strategies)\n    \n    # Small delay between tests\n    await asyncio.sleep(2)\n    \n    # Run parallel processing\n    print(f\"\\n[{timestamp()}] ⚡ Running Parallel Processing Test...\")\n    parallel_results = await comparison.run_parallel_processing(test_data, strategies)\n    \n    # Compare results\n    comparison.compare_results(sequential_results, parallel_results)\n    \n    print(f\"\\n[{timestamp()}] ✅ Comparison completed successfully!\")\n\nif __name__ == \"__main__\":\n    asyncio.run(main())