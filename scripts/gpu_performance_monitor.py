#!/usr/bin/env python3
"""
GPU Performance Monitor for Strategy Evolution
Monitors GPU utilization, memory usage, and processing times
"""

import time
import psutil
import torch
import cupy as cp
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any
import logging
import threading
import json
from pathlib import Path

# Configure logging with timestamps
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s.%(msecs)03d - %(name)s - %(levelname)s - %(message)s',
    datefmt='%H:%M:%S'
)
logger = logging.getLogger(__name__)

class GPUPerformanceMonitor:
    """Monitor GPU performance during strategy evolution"""
    
    def __init__(self, log_interval: float = 1.0):
        self.log_interval = log_interval
        self.monitoring = False
        self.monitor_thread = None
        self.performance_data = []
        self.start_time = None
        
        # Check GPU availability
        self.cuda_available = torch.cuda.is_available()
        self.device_count = torch.cuda.device_count() if self.cuda_available else 0
        
        if self.cuda_available:
            print(f"{self._timestamp()} 🚀 GPU Performance Monitor initialized")
            print(f"{self._timestamp()} 📊 Monitoring {self.device_count} GPU(s)")
            for i in range(self.device_count):
                gpu_name = torch.cuda.get_device_name(i)
                gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                print(f"{self._timestamp()} 🎯 GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)")
        else:
            print(f"{self._timestamp()} ⚠️ CUDA not available - CPU monitoring only")
    
    def _timestamp(self) -> str:
        """Get formatted timestamp"""
        return datetime.now().strftime('%H:%M:%S.%f')[:-3]
    
    def start_monitoring(self):
        """Start performance monitoring"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.start_time = datetime.now()
        self.performance_data = []
        
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        print(f"{self._timestamp()} 📈 Performance monitoring started")
        logger.info("Performance monitoring started")
    
    def stop_monitoring(self) -> Dict[str, Any]:
        """Stop monitoring and return performance summary"""
        if not self.monitoring:
            return {}
        
        self.monitoring = False
        
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2.0)
        
        # Calculate summary statistics
        summary = self._calculate_summary()
        
        print(f"{self._timestamp()} 📊 Performance monitoring stopped")
        self._print_performance_summary(summary)
        
        # Save detailed data to file
        self._save_performance_data(summary)
        
        return summary
    
    def _monitor_loop(self):
        """Main monitoring loop"""
        while self.monitoring:
            try:
                timestamp = datetime.now()
                data_point = {
                    'timestamp': timestamp.isoformat(),
                    'elapsed_seconds': (timestamp - self.start_time).total_seconds(),
                    'cpu_percent': psutil.cpu_percent(interval=None),
                    'memory_percent': psutil.virtual_memory().percent,
                    'memory_used_gb': psutil.virtual_memory().used / 1024**3,
                    'memory_available_gb': psutil.virtual_memory().available / 1024**3
                }
                
                # GPU metrics
                if self.cuda_available:
                    gpu_metrics = []
                    for device_id in range(self.device_count):
                        try:
                            with torch.cuda.device(device_id):
                                # Memory usage
                                memory_allocated = torch.cuda.memory_allocated(device_id) / 1024**3
                                memory_reserved = torch.cuda.memory_reserved(device_id) / 1024**3
                                memory_total = torch.cuda.get_device_properties(device_id).total_memory / 1024**3
                                
                                # Utilization (approximated)
                                utilization = (memory_allocated / memory_total) * 100 if memory_total > 0 else 0
                                
                                gpu_metrics.append({
                                    'device_id': device_id,
                                    'memory_allocated_gb': memory_allocated,
                                    'memory_reserved_gb': memory_reserved,
                                    'memory_total_gb': memory_total,
                                    'memory_utilization_percent': utilization,
                                    'memory_free_gb': memory_total - memory_reserved
                                })
                        except Exception as e:
                            logger.warning(f"Error getting GPU {device_id} metrics: {e}")
                    
                    data_point['gpu_metrics'] = gpu_metrics
                
                self.performance_data.append(data_point)
                
                # Print periodic updates
                if len(self.performance_data) % 10 == 0:  # Every 10 seconds
                    self._print_current_status(data_point)
                
                time.sleep(self.log_interval)
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(self.log_interval)
    
    def _print_current_status(self, data_point: Dict[str, Any]):
        """Print current performance status"""
        elapsed = data_point['elapsed_seconds']
        cpu_percent = data_point['cpu_percent']
        memory_percent = data_point['memory_percent']
        memory_used = data_point['memory_used_gb']
        
        status_line = f"{self._timestamp()} 📊 [{elapsed:6.1f}s] CPU: {cpu_percent:5.1f}% | RAM: {memory_percent:5.1f}% ({memory_used:.1f}GB)"
        
        if self.cuda_available and 'gpu_metrics' in data_point:
            gpu_status = []
            for gpu in data_point['gpu_metrics']:
                gpu_util = gpu['memory_utilization_percent']
                gpu_mem = gpu['memory_allocated_gb']
                gpu_status.append(f"GPU{gpu['device_id']}: {gpu_util:5.1f}% ({gpu_mem:.1f}GB)")
            
            status_line += " | " + " | ".join(gpu_status)
        
        print(status_line)
    
    def _calculate_summary(self) -> Dict[str, Any]:
        """Calculate performance summary statistics"""
        if not self.performance_data:
            return {}
        
        # CPU and memory statistics
        cpu_values = [d['cpu_percent'] for d in self.performance_data]
        memory_values = [d['memory_percent'] for d in self.performance_data]
        memory_used_values = [d['memory_used_gb'] for d in self.performance_data]
        
        summary = {
            'monitoring_duration_seconds': self.performance_data[-1]['elapsed_seconds'],
            'data_points_collected': len(self.performance_data),
            'cpu_stats': {
                'avg_percent': np.mean(cpu_values),
                'max_percent': np.max(cpu_values),
                'min_percent': np.min(cpu_values),
                'std_percent': np.std(cpu_values)
            },
            'memory_stats': {
                'avg_percent': np.mean(memory_values),
                'max_percent': np.max(memory_values),
                'min_percent': np.min(memory_values),
                'avg_used_gb': np.mean(memory_used_values),
                'max_used_gb': np.max(memory_used_values)
            }
        }
        
        # GPU statistics
        if self.cuda_available:
            gpu_summary = {}
            for device_id in range(self.device_count):
                device_data = []
                for data_point in self.performance_data:
                    if 'gpu_metrics' in data_point:
                        for gpu in data_point['gpu_metrics']:
                            if gpu['device_id'] == device_id:
                                device_data.append(gpu)
                                break
                
                if device_data:
                    utilization_values = [d['memory_utilization_percent'] for d in device_data]
                    allocated_values = [d['memory_allocated_gb'] for d in device_data]
                    
                    gpu_summary[f'gpu_{device_id}'] = {
                        'avg_utilization_percent': np.mean(utilization_values),
                        'max_utilization_percent': np.max(utilization_values),
                        'avg_memory_allocated_gb': np.mean(allocated_values),
                        'max_memory_allocated_gb': np.max(allocated_values),
                        'total_memory_gb': device_data[0]['memory_total_gb']
                    }
            
            summary['gpu_stats'] = gpu_summary
        
        return summary
    
    def _print_performance_summary(self, summary: Dict[str, Any]):
        """Print performance summary"""
        if not summary:
            return
        
        duration = summary['monitoring_duration_seconds']
        data_points = summary['data_points_collected']
        
        print(f"\n{self._timestamp()} 📊 PERFORMANCE SUMMARY")
        print("=" * 80)
        print(f"⏱️  Duration: {duration:.1f} seconds ({data_points} data points)")
        
        # CPU stats
        cpu_stats = summary['cpu_stats']
        print(f"🖥️  CPU Usage: {cpu_stats['avg_percent']:.1f}% avg, {cpu_stats['max_percent']:.1f}% max")
        
        # Memory stats
        mem_stats = summary['memory_stats']
        print(f"💾 RAM Usage: {mem_stats['avg_percent']:.1f}% avg ({mem_stats['avg_used_gb']:.1f}GB), {mem_stats['max_percent']:.1f}% max ({mem_stats['max_used_gb']:.1f}GB)")
        
        # GPU stats
        if 'gpu_stats' in summary:
            for gpu_id, gpu_stats in summary['gpu_stats'].items():
                device_num = gpu_id.split('_')[1]
                print(f"🚀 GPU {device_num}: {gpu_stats['avg_utilization_percent']:.1f}% avg, {gpu_stats['max_utilization_percent']:.1f}% max utilization")
                print(f"    Memory: {gpu_stats['avg_memory_allocated_gb']:.1f}GB avg, {gpu_stats['max_memory_allocated_gb']:.1f}GB max (of {gpu_stats['total_memory_gb']:.1f}GB total)")
        
        print("=" * 80)
    
    def _save_performance_data(self, summary: Dict[str, Any]):
        """Save performance data to file"""
        try:
            # Create logs directory if it doesn't exist
            logs_dir = Path("logs")
            logs_dir.mkdir(exist_ok=True)
            
            # Save detailed data
            timestamp_str = datetime.now().strftime('%Y%m%d_%H%M%S')
            detailed_file = logs_dir / f"gpu_performance_detailed_{timestamp_str}.json"
            
            with open(detailed_file, 'w') as f:
                json.dump({
                    'summary': summary,
                    'detailed_data': self.performance_data
                }, f, indent=2, default=str)
            
            # Save summary only
            summary_file = logs_dir / f"gpu_performance_summary_{timestamp_str}.json"
            with open(summary_file, 'w') as f:
                json.dump(summary, f, indent=2, default=str)
            
            print(f"{self._timestamp()} 💾 Performance data saved to {detailed_file}")
            logger.info(f"Performance data saved to {detailed_file}")
            
        except Exception as e:
            logger.error(f"Error saving performance data: {e}")
    
    def get_current_gpu_usage(self) -> Dict[str, Any]:
        """Get current GPU usage snapshot"""
        if not self.cuda_available:
            return {'error': 'CUDA not available'}
        
        gpu_usage = {}
        for device_id in range(self.device_count):
            try:
                with torch.cuda.device(device_id):
                    memory_allocated = torch.cuda.memory_allocated(device_id) / 1024**3
                    memory_reserved = torch.cuda.memory_reserved(device_id) / 1024**3
                    memory_total = torch.cuda.get_device_properties(device_id).total_memory / 1024**3
                    
                    gpu_usage[f'gpu_{device_id}'] = {
                        'memory_allocated_gb': memory_allocated,
                        'memory_reserved_gb': memory_reserved,
                        'memory_total_gb': memory_total,
                        'memory_utilization_percent': (memory_allocated / memory_total) * 100,
                        'memory_free_gb': memory_total - memory_reserved
                    }
            except Exception as e:
                gpu_usage[f'gpu_{device_id}'] = {'error': str(e)}
        
        return gpu_usage

# Global instance
gpu_performance_monitor = GPUPerformanceMonitor()

def monitor_gpu_performance(func):
    """Decorator to monitor GPU performance during function execution"""
    def wrapper(*args, **kwargs):
        gpu_performance_monitor.start_monitoring()
        try:
            result = func(*args, **kwargs)
            return result
        finally:
            summary = gpu_performance_monitor.stop_monitoring()
            return result, summary
    return wrapper

async def monitor_gpu_performance_async(func):
    """Async decorator to monitor GPU performance during function execution"""
    async def wrapper(*args, **kwargs):
        gpu_performance_monitor.start_monitoring()
        try:
            result = await func(*args, **kwargs)
            return result
        finally:
            summary = gpu_performance_monitor.stop_monitoring()
            return result, summary
    return wrapper

if __name__ == "__main__":
    # Test the performance monitor
    print("🧪 Testing GPU Performance Monitor")
    
    monitor = GPUPerformanceMonitor(log_interval=0.5)
    monitor.start_monitoring()
    
    # Simulate some work
    print("⚡ Simulating GPU work...")
    if torch.cuda.is_available():
        # Create some GPU tensors to use memory
        tensors = []
        for i in range(5):
            tensor = torch.randn(1000, 1000, device='cuda')
            tensors.append(tensor)
            time.sleep(1)
        
        # Do some GPU operations
        for i in range(10):
            result = torch.matmul(tensors[0], tensors[1])
            time.sleep(0.5)
        
        # Clean up
        del tensors
        torch.cuda.empty_cache()
    else:
        # CPU simulation
        for i in range(10):
            _ = np.random.random((1000, 1000))
            time.sleep(0.5)
    
    print("✅ Work completed, stopping monitor...")
    summary = monitor.stop_monitoring()