#!/usr/bin/env python3
"""
Test script to verify batch processing optimizations
"""

import asyncio
import time
from datetime import datetime
from utils.gpu_parallel_processor import gpu_parallel_processor, GPUTask
import numpy as np

def timestamp():
    return datetime.now().strftime('%H:%M:%S.%f')[:-3]

async def test_batch_optimization():
    """Test optimized batch processing performance"""
    
    print("🧪 Testing Batch Processing Optimizations")
    print("=" * 60)
    
    # Test 1: Check pre-warming and pre-allocation
    print(f"\n1. Testing GPU Pre-warming and Resource Pre-allocation...")
    print(f"   ✅ GPU pre-warming completed: {gpu_parallel_processor._warm_up_completed}")
    print(f"   ✅ Pre-allocated arrays: {len(gpu_parallel_processor._pre_allocated_arrays)} devices")
    
    if gpu_parallel_processor.cuda_available:
        for device_id, arrays in gpu_parallel_processor._pre_allocated_arrays.items():
            print(f"   📊 Device {device_id}: {len(arrays)} pre-allocated arrays")
    
    # Test 2: Create test batches
    print(f"\n2. Creating Test Batches...")
    
    def create_test_batch(batch_id, num_tasks=8):
        """Create a test batch with specified number of tasks"""
        tasks = []
        
        # Create sample data
        n_rows = 2000
        test_data = {
            'close': np.random.random(n_rows).astype(np.float32),
            'high': np.random.random(n_rows).astype(np.float32),
            'low': np.random.random(n_rows).astype(np.float32),
            'volume': np.random.random(n_rows).astype(np.float32)
        }
        
        # Create test strategies
        test_strategies = []
        for i in range(3):  # 3 strategies per task
            test_strategies.append({
                'name': f'TestStrategy_{i}',
                'type': 'RSI_Reversal'
            })
        
        # Create tasks
        for i in range(num_tasks):
            task = GPUTask(
                task_id=f"batch_{batch_id}_task_{i}",
                data=test_data.copy(),
                strategies=test_strategies.copy()
            )
            tasks.append(task)
        
        return tasks
    
    # Test 3: Sequential batch processing (simulating the evolution pipeline)
    print(f"\n3. Testing Sequential Batch Processing Performance...")
    
    num_batches = 5
    tasks_per_batch = 8
    batch_times = []
    
    print(f"   📊 Processing {num_batches} batches with {tasks_per_batch} tasks each")
    
    total_start_time = time.time()
    
    for batch_num in range(num_batches):
        print(f"\n   🔥 Batch {batch_num + 1}/{num_batches}")
        
        # Create batch
        batch_start = time.time()
        test_batch = create_test_batch(batch_num, tasks_per_batch)
        creation_time = time.time() - batch_start
        
        print(f"   [{timestamp()}] 📋 Batch created in {creation_time:.3f}s")
        
        # Process batch
        process_start = time.time()
        try:
            results = await gpu_parallel_processor.process_batch_parallel(test_batch)
            process_time = time.time() - process_start
            
            print(f"   [{timestamp()}] ✅ Batch processed in {process_time:.3f}s ({len(results)} results)")
            batch_times.append(process_time)
            
        except Exception as e:
            print(f"   [{timestamp()}] ❌ Batch failed: {e}")
            batch_times.append(float('inf'))
        
        # Small delay to simulate real conditions
        await asyncio.sleep(0.05)
    
    total_time = time.time() - total_start_time
    
    # Test 4: Performance Analysis
    print(f"\n4. Performance Analysis...")
    
    if batch_times and all(t != float('inf') for t in batch_times):
        avg_batch_time = sum(batch_times) / len(batch_times)
        min_batch_time = min(batch_times)
        max_batch_time = max(batch_times)
        
        print(f"   📊 Total processing time: {total_time:.3f}s")
        print(f"   📊 Average batch time: {avg_batch_time:.3f}s")
        print(f"   📊 Fastest batch: {min_batch_time:.3f}s")
        print(f"   📊 Slowest batch: {max_batch_time:.3f}s")
        print(f"   📊 Batch time consistency: {(max_batch_time - min_batch_time):.3f}s variation")
        
        # Calculate throughput
        total_tasks = num_batches * tasks_per_batch
        throughput = total_tasks / total_time
        print(f"   🚀 Overall throughput: {throughput:.1f} tasks/second")
        
        # Performance rating
        if avg_batch_time < 2.0:
            print(f"   🎉 EXCELLENT: Average batch time under 2 seconds!")
        elif avg_batch_time < 5.0:
            print(f"   ✅ GOOD: Average batch time under 5 seconds")
        elif avg_batch_time < 10.0:
            print(f"   ⚠️ FAIR: Average batch time under 10 seconds")
        else:
            print(f"   ❌ POOR: Average batch time over 10 seconds")
    
    # Test 5: Memory optimization check
    print(f"\n5. Testing Memory Optimization...")
    
    if gpu_parallel_processor.cuda_available:
        try:
            import cupy as cp
            
            # Check memory usage
            for device_id in range(gpu_parallel_processor.device_count):
                with cp.cuda.Device(device_id):
                    mempool = cp.get_default_memory_pool()
                    used_bytes = mempool.used_bytes()
                    total_bytes = mempool.total_bytes()
                    
                    print(f"   📊 GPU {device_id} memory: {used_bytes/1024**3:.2f}GB used / {total_bytes/1024**3:.2f}GB total")
                    print(f"   📊 Memory utilization: {(used_bytes/total_bytes*100):.1f}%")
            
            # Test smart cleanup
            print(f"   🧹 Testing smart memory cleanup...")
            cleanup_start = time.time()
            gpu_parallel_processor.cleanup_gpu_memory(force=False)
            cleanup_time = time.time() - cleanup_start
            print(f"   ✅ Smart cleanup completed in {cleanup_time:.3f}s")
            
        except ImportError:
            print("   ⚠️ CuPy not available for memory testing")
    else:
        print("   ⚠️ CUDA not available for memory testing")
    
    print("\n" + "=" * 60)
    print("🎉 Batch Processing Optimization Test Complete!")
    
    if batch_times and all(t != float('inf') for t in batch_times):
        print("📊 OPTIMIZATION SUMMARY:")
        print(f"   • GPU pre-warming: {'✅ Enabled' if gpu_parallel_processor._warm_up_completed else '❌ Disabled'}")
        print(f"   • Resource pre-allocation: {'✅ Enabled' if gpu_parallel_processor._pre_allocated_arrays else '❌ Disabled'}")
        print(f"   • Average batch time: {avg_batch_time:.3f}s")
        print(f"   • Processing throughput: {throughput:.1f} tasks/second")
        print(f"   • Memory optimization: ✅ Smart cleanup enabled")

if __name__ == "__main__":
    asyncio.run(test_batch_optimization())
