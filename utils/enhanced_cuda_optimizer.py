#!/usr/bin/env python3
"""
Enhanced CUDA Optimizer with Better Memory Management and Strategy Processing

This module provides improved CUDA optimization specifically for strategy evolution
with better memory management and parallel processing capabilities.
"""

import os
import logging
import numpy as np
from typing import Dict, Any, Optional, Tuple, List
from pathlib import Path

logger = logging.getLogger(__name__)

class EnhancedCUDAOptimizer:
    """Enhanced CUDA optimizer with better strategy processing"""
    
    def __init__(self):
        self.cuda_available = False
        self.device_count = 0
        self.memory_info = {}
        self.config = self._load_config()
        self._init_cuda()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load CUDA configuration"""
        try:
            import yaml
            config_path = Path("config/enhanced_cuda_optimization_config.yaml")
            if config_path.exists():
                with open(config_path, 'r') as f:
                    return yaml.safe_load(f)
        except Exception as e:

        # Default configuration
        return {
            'cuda': {'enabled': True, 'memory_fraction': 0.8},
            'parallel_processing': {
                'strategies_per_batch': 2,
                'concurrent_files': 3,
                'max_workers': 4
            },
            'memory_management': {
                'cleanup_frequency': 5,
                'max_memory_usage': 0.85,
                'batch_size_multiplier': 0.5
            },
            'backtesting': {
                'min_data_size_for_gpu': 1000,
                'min_strategies_for_parallel': 2,
                'max_concurrent_strategies': 2
            }
        }
    
    def _init_cuda(self):
        """Initialize CUDA with proper error handling"""
        try:
            # Set environment variables for better CUDA performance
            os.environ['CUDA_LAUNCH_BLOCKING'] = '0'  # Async kernel launches
            os.environ['CUDA_CACHE_DISABLE'] = '0'    # Enable caching
            
            import torch
            if torch.cuda.is_available():
                self.cuda_available = True
                self.device_count = torch.cuda.device_count()
                
                # Get memory info for first device
                device = torch.cuda.get_device_properties(0)
                total_memory = device.total_memory / (1024**3)  # GB
                
                self.memory_info = {
                    'device_name': device.name,
                    'memory_total': total_memory,
                    'compute_capability': f"{device.major}.{device.minor}"
                }
                
                # Set memory fraction
                memory_fraction = self.config['cuda'].get('memory_fraction', 0.8)
                torch.cuda.set_per_process_memory_fraction(memory_fraction)
                
                logger.info(f"🚀 Enhanced CUDA initialized: {device.name} ({total_memory:.1f}GB)")
            else:
                logger.info("⚠️ CUDA not available")
                
        except Exception as e:
            logger.warning(f"CUDA initialization failed: {e}")
            self.cuda_available = False
    
    def should_use_cuda(self, data_size: int, signal_count: int = 0, trade_count: int = 0) -> bool:
        """Determine if CUDA should be used based on data size and complexity"""
        if not self.cuda_available:
            return False
        
        config = self.config['backtesting']
        
        # Check minimum data size
        if data_size < config.get('min_data_size_for_gpu', 1000):
            return False
        
        # For strategy processing, consider signal count
        if signal_count > 0 and signal_count < 10:
            return False
        
        # For performance metrics, consider trade count
        if trade_count > 0 and trade_count < 50:
            return False
        
        return True
    
    def get_optimal_batch_size(self, total_size: int) -> int:
        """Calculate optimal batch size based on available memory"""
        if not self.cuda_available:
            return total_size
        
        try:
            import torch
            
            # Get available memory
            available_memory = torch.cuda.get_device_properties(0).total_memory
            memory_fraction = self.config['cuda'].get('memory_fraction', 0.8)
            usable_memory = available_memory * memory_fraction
            
            # Estimate memory per item (rough calculation)
            memory_per_item = 1024  # bytes per data point
            max_items = int(usable_memory / memory_per_item)
            
            # Apply batch size multiplier from config
            multiplier = self.config['memory_management'].get('batch_size_multiplier', 0.5)
            optimal_batch = int(min(max_items * multiplier, total_size))
            
            return max(1000, optimal_batch)  # Minimum batch size
            
        except Exception as e:

            return min(10000, total_size)
    
    def get_cuda_kernel_config(self, data_size: int) -> Tuple[int, int]:
        """Get optimal CUDA kernel configuration"""
        threads_per_block = 256  # Good default for most GPUs
        blocks_per_grid = (data_size + threads_per_block - 1) // threads_per_block
        
        # Limit blocks to prevent resource exhaustion
        max_blocks = 65535  # CUDA limit
        blocks_per_grid = min(blocks_per_grid, max_blocks)
        
        return blocks_per_grid, threads_per_block
    
    def cleanup_memory(self):
        """Clean up GPU memory"""
        if not self.cuda_available:
            return
        
        try:
            import torch
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.synchronize()

        except Exception as e:

    def get_memory_usage(self) -> Dict[str, float]:
        """Get current GPU memory usage"""
        if not self.cuda_available:
            return {'allocated': 0, 'cached': 0, 'total': 0}
        
        try:
            import torch
            allocated = torch.cuda.memory_allocated() / (1024**3)  # GB
            cached = torch.cuda.memory_reserved() / (1024**3)      # GB
            total = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            
            return {
                'allocated': allocated,
                'cached': cached,
                'total': total,
                'usage_percent': (allocated / total) * 100
            }
        except Exception as e:

            return {'allocated': 0, 'cached': 0, 'total': 0}
    
    def optimize_for_strategy_evolution(self) -> Dict[str, Any]:
        """Apply optimizations specifically for strategy evolution"""
        optimizations = []
        
        if not self.cuda_available:
            return {'optimizations_applied': optimizations, 'cuda_available': False}
        
        try:
            import torch
            
            # Enable optimizations
            torch.backends.cudnn.benchmark = True  # Optimize for consistent input sizes
            torch.backends.cudnn.deterministic = False  # Allow non-deterministic for speed
            
            # Set optimal number of threads
            num_threads = min(4, os.cpu_count())  # Limit to prevent NumExpr hanging
            torch.set_num_threads(num_threads)
            
            optimizations.extend([
                'cudnn_benchmark_enabled',
                'non_deterministic_mode',
                f'cpu_threads_set_to_{num_threads}'
            ])
            
            # Configure memory management
            memory_config = self.config['memory_management']
            if memory_config.get('max_memory_usage', 0.85) < 1.0:
                torch.cuda.set_per_process_memory_fraction(
                    memory_config['max_memory_usage']
                )
                optimizations.append('memory_fraction_limited')
            
            logger.info(f"🚀 Strategy evolution optimizations applied: {len(optimizations)}")
            
        except Exception as e:
            logger.warning(f"Strategy evolution optimization failed: {e}")
        
        return {
            'optimizations_applied': optimizations,
            'cuda_available': self.cuda_available,
            'memory_info': self.memory_info
        }
    
    def process_strategies_parallel(self, data: np.ndarray, strategies: List[Dict], 
                                  symbol: str) -> Dict[str, np.ndarray]:
        """Process multiple strategies in parallel with improved error handling"""
        if not self.cuda_available or len(strategies) < 2:
            return {}
        
        try:
            from numba import cuda
            import torch
            
            n_rows, n_cols = data.shape
            n_strategies = len(strategies)
            
            # Limit concurrent strategies based on config
            max_concurrent = self.config['backtesting'].get('max_concurrent_strategies', 2)
            strategies_to_process = strategies[:max_concurrent]
            
            logger.info(f"🚀 Processing {len(strategies_to_process)} strategies in parallel for {symbol}")
            
            # Convert to GPU arrays
            data_gpu = torch.from_numpy(data.astype(np.float32)).cuda()
            results = {}
            
            # Process strategies in smaller batches
            batch_size = self.config['parallel_processing'].get('strategies_per_batch', 2)
            
            for i in range(0, len(strategies_to_process), batch_size):
                batch_strategies = strategies_to_process[i:i+batch_size]
                
                # Process each strategy in the batch
                for j, strategy in enumerate(batch_strategies):
                    strategy_name = strategy.get('name', f'Strategy_{i+j}')
                    
                    try:
                        # Simple parallel processing logic
                        signals = self._process_single_strategy_gpu(data_gpu, strategy)
                        if signals is not None:
                            results[strategy_name] = signals.cpu().numpy()

                        else:
                            logger.warning(f"❌ Failed to process {strategy_name}")
                    
                    except Exception as e:
                        logger.error(f"Error processing {strategy_name}: {e}")
                        continue
                
                # Clean up after each batch
                if i % 2 == 0:  # Every 2 batches
                    self.cleanup_memory()
            
            logger.info(f"🚀 Parallel processing completed: {len(results)} strategies")
            return results
            
        except Exception as e:
            logger.error(f"Parallel strategy processing failed: {e}")
            self.cleanup_memory()
            return {}
    
    def _process_single_strategy_gpu(self, data_gpu, strategy: Dict) -> Optional[torch.Tensor]:
        """Process a single strategy on GPU"""
        try:
            import torch
            
            n_rows = data_gpu.shape[0]
            signals = torch.zeros(n_rows, dtype=torch.int32, device='cuda')
            
            # Extract price data (assuming close is column 3)
            if data_gpu.shape[1] > 3:
                close_prices = data_gpu[:, 3]
                
                # Simple moving average strategy
                window_size = 20
                if n_rows > window_size:
                    # Calculate SMA using unfold for efficiency
                    sma = close_prices.unfold(0, window_size, 1).mean(dim=1)
                    
                    # Pad to match original size
                    sma_padded = torch.cat([
                        torch.zeros(window_size-1, device='cuda'),
                        sma
                    ])
                    
                    # Generate signals
                    buy_condition = close_prices > sma_padded * 1.02
                    sell_condition = close_prices < sma_padded * 0.98
                    
                    signals[buy_condition] = 1
                    signals[sell_condition] = -1
            
            return signals
            
        except Exception as e:

            return None

# Global instance
enhanced_cuda_optimizer = None

def get_enhanced_cuda_optimizer():
    """Get global enhanced CUDA optimizer instance"""
    global enhanced_cuda_optimizer
    if enhanced_cuda_optimizer is None:
        enhanced_cuda_optimizer = EnhancedCUDAOptimizer()
    return enhanced_cuda_optimizer

def get_cuda_optimizer():
    """Get CUDA optimizer instance (compatibility function)"""
    return get_enhanced_cuda_optimizer()

def optimize_for_strategy_evolution():
    """Apply strategy evolution optimizations"""
    optimizer = get_enhanced_cuda_optimizer()
    return optimizer.optimize_for_strategy_evolution()