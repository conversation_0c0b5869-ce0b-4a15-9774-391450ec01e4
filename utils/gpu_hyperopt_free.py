#!/usr/bin/env python3
"""
Free GPU-Accelerated Hyperparameter Optimization
Alternative to Optuna using Scikit-Optimize, Hyperopt, and Ray Tune
Optimized for RTX 3060Ti with 8GB VRAM
"""

import numpy as np
import logging
from typing import Dict, List, Any, Callable, Tuple, Optional
import time
import concurrent.futures
from dataclasses import dataclass
import json
import pickle
from pathlib import Path

# Free optimization libraries
try:
    from skopt import gp_minimize, forest_minimize, gbrt_minimize
    from skopt.space import Real, Integer, Categorical
    from skopt.utils import use_named_args
    from skopt.acquisition import gaussian_ei, gaussian_lcb
    SKOPT_AVAILABLE = True
except ImportError:
    SKOPT_AVAILABLE = False

try:
    from hyperopt import fmin, tpe, hp, Trials, STATUS_OK, rand
    from hyperopt.early_stop import no_progress_loss
    HYPEROPT_AVAILABLE = True
except ImportError:
    HYPEROPT_AVAILABLE = False

try:
    import ray
    from ray import tune
    from ray.tune.schedulers import ASHAScheduler, PopulationBasedTraining
    from ray.tune.suggest.skopt import SkOptSearch
    from ray.tune.suggest.hyperopt import HyperOptSearch
    RAY_AVAILABLE = True
except ImportError:
    RAY_AVAILABLE = False

# GPU acceleration
try:
    import cupy as cp
    import torch
    GPU_AVAILABLE = torch.cuda.is_available()
except ImportError:
    GPU_AVAILABLE = False

logger = logging.getLogger(__name__)

@dataclass
class OptimizationResult:
    """Result of hyperparameter optimization"""
    best_params: Dict[str, Any]
    best_score: float
    optimization_time: float
    n_trials: int
    method: str
    convergence_history: List[float]

class GPUHyperoptFree:
    """Free GPU-accelerated hyperparameter optimization"""
    
    def __init__(self, gpu_memory_fraction: float = 0.8):
        self.gpu_available = GPU_AVAILABLE
        self.gpu_memory_fraction = gpu_memory_fraction
        
        # Initialize available optimizers
        self.optimizers = self._detect_available_optimizers()
        
        if self.gpu_available:
            self._setup_gpu()
            logger.info(f"🚀 GPU Hyperopt initialized with {len(self.optimizers)} optimizers")
        else:
            logger.info(f"💻 CPU Hyperopt initialized with {len(self.optimizers)} optimizers")
    
    def _detect_available_optimizers(self) -> List[str]:
        """Detect available optimization libraries"""
        optimizers = []
        
        if SKOPT_AVAILABLE:
            optimizers.extend(['skopt_gp', 'skopt_forest', 'skopt_gbrt'])
        
        if HYPEROPT_AVAILABLE:
            optimizers.extend(['hyperopt_tpe', 'hyperopt_random'])
        
        if RAY_AVAILABLE:
            optimizers.extend(['ray_tune_asha', 'ray_tune_pbt'])
        
        # Always available fallback
        optimizers.append('random_search')
        
        return optimizers
    
    def _setup_gpu(self):
        """Setup GPU for optimization"""
        try:
            if torch.cuda.is_available():
                # Set memory fraction
                torch.cuda.set_per_process_memory_fraction(self.gpu_memory_fraction)
                
                # Enable optimizations
                torch.backends.cudnn.benchmark = True
                torch.backends.cudnn.deterministic = False
                
            if cp:
                # Setup CuPy memory pool
                mempool = cp.get_default_memory_pool()
                mempool.set_limit(size=int(6 * 1024**3))  # 6GB limit
                
        except Exception as e:
            logger.warning(f"GPU setup failed: {e}")
    
    def optimize_strategy_parameters(self, 
                                   objective_func: Callable,
                                   param_space: Dict[str, Any],
                                   n_trials: int = 50,
                                   method: str = 'auto',
                                   parallel_jobs: int = 4) -> OptimizationResult:
        """
        Optimize strategy parameters using the best available method
        
        Args:
            objective_func: Function to optimize (higher is better)
            param_space: Parameter space definition
            n_trials: Number of optimization trials
            method: Optimization method ('auto', 'skopt_gp', 'hyperopt_tpe', etc.)
            parallel_jobs: Number of parallel jobs
        
        Returns:
            OptimizationResult with best parameters and metrics
        """
        start_time = time.time()
        
        # Auto-select best method
        if method == 'auto':
            method = self._select_best_method(n_trials, len(param_space))
        
        logger.info(f"🎯 Starting optimization with {method} - {n_trials} trials")
        
        try:
            if method.startswith('skopt_'):
                result = self._optimize_with_skopt(objective_func, param_space, n_trials, method, parallel_jobs)
            elif method.startswith('hyperopt_'):
                result = self._optimize_with_hyperopt(objective_func, param_space, n_trials, method)
            elif method.startswith('ray_'):
                result = self._optimize_with_ray(objective_func, param_space, n_trials, method)
            else:
                result = self._optimize_with_random_search(objective_func, param_space, n_trials, parallel_jobs)
            
            optimization_time = time.time() - start_time
            result.optimization_time = optimization_time
            result.method = method
            
            logger.info(f"✅ Optimization completed in {optimization_time:.2f}s - Best score: {result.best_score:.4f}")
            return result
            
        except Exception as e:
            logger.error(f"Optimization failed with {method}: {e}")
            # Fallback to random search
            return self._optimize_with_random_search(objective_func, param_space, n_trials, parallel_jobs)
    
    def _select_best_method(self, n_trials: int, n_params: int) -> str:
        """Select the best optimization method based on problem size"""
        
        # For small problems, use Gaussian Process
        if n_trials <= 20 and n_params <= 5 and 'skopt_gp' in self.optimizers:
            return 'skopt_gp'
        
        # For medium problems, use Tree-based methods
        if n_trials <= 100 and n_params <= 10:
            if 'hyperopt_tpe' in self.optimizers:
                return 'hyperopt_tpe'
            elif 'skopt_forest' in self.optimizers:
                return 'skopt_forest'
        
        # For large problems, use Ray Tune with early stopping
        if n_trials > 100 and 'ray_tune_asha' in self.optimizers:
            return 'ray_tune_asha'
        
        # Default fallback
        if 'skopt_gp' in self.optimizers:
            return 'skopt_gp'
        elif 'hyperopt_tpe' in self.optimizers:
            return 'hyperopt_tpe'
        else:
            return 'random_search'
    
    def _convert_param_space_to_skopt(self, param_space: Dict[str, Any]) -> List:
        """Convert parameter space to scikit-optimize format"""
        dimensions = []
        param_names = []
        
        for name, spec in param_space.items():
            param_names.append(name)
            
            if spec['type'] == 'float':
                dimensions.append(Real(spec['low'], spec['high'], name=name))
            elif spec['type'] == 'int':
                dimensions.append(Integer(spec['low'], spec['high'], name=name))
            elif spec['type'] == 'categorical':
                dimensions.append(Categorical(spec['choices'], name=name))
        
        return dimensions, param_names
    
    def _optimize_with_skopt(self, objective_func, param_space, n_trials, method, parallel_jobs):
        """Optimize using scikit-optimize"""
        dimensions, param_names = self._convert_param_space_to_skopt(param_space)
        
        @use_named_args(dimensions)
        def objective(**params):
            try:
                # Convert to negative because skopt minimizes
                return -objective_func(params)
            except Exception as e:
                logger.warning(f"Objective evaluation failed: {e}")
                return 0.0
        
        # Select optimizer
        if method == 'skopt_gp':
            optimizer_func = gp_minimize
            acq_func = 'EI'  # Expected Improvement
        elif method == 'skopt_forest':
            optimizer_func = forest_minimize
            acq_func = 'EI'
        else:  # skopt_gbrt
            optimizer_func = gbrt_minimize
            acq_func = 'EI'
        
        # Run optimization
        result = optimizer_func(
            func=objective,
            dimensions=dimensions,
            n_calls=n_trials,
            n_jobs=parallel_jobs if parallel_jobs > 1 else 1,
            acq_func=acq_func,
            random_state=42
        )
        
        # Extract results
        best_params = dict(zip(param_names, result.x))
        best_score = -result.fun  # Convert back to positive
        convergence_history = [-y for y in result.func_vals]
        
        return OptimizationResult(
            best_params=best_params,
            best_score=best_score,
            optimization_time=0.0,  # Will be set by caller
            n_trials=len(result.func_vals),
            method=method,
            convergence_history=convergence_history
        )
    
    def _convert_param_space_to_hyperopt(self, param_space: Dict[str, Any]) -> Dict:
        """Convert parameter space to hyperopt format"""
        space = {}
        
        for name, spec in param_space.items():
            if spec['type'] == 'float':
                space[name] = hp.uniform(name, spec['low'], spec['high'])
            elif spec['type'] == 'int':
                space[name] = hp.randint(name, spec['low'], spec['high'] + 1)
            elif spec['type'] == 'categorical':
                space[name] = hp.choice(name, spec['choices'])
        
        return space
    
    def _optimize_with_hyperopt(self, objective_func, param_space, n_trials, method):
        """Optimize using hyperopt"""
        space = self._convert_param_space_to_hyperopt(param_space)
        
        def objective(params):
            try:
                score = objective_func(params)
                return {'loss': -score, 'status': STATUS_OK}  # Hyperopt minimizes
            except Exception as e:
                logger.warning(f"Objective evaluation failed: {e}")
                return {'loss': 0.0, 'status': STATUS_OK}
        
        # Select algorithm
        if method == 'hyperopt_tpe':
            algo = tpe.suggest
        else:  # hyperopt_random
            algo = rand.suggest
        
        # Run optimization
        trials = Trials()
        best = fmin(
            fn=objective,
            space=space,
            algo=algo,
            max_evals=n_trials,
            trials=trials,
            rstate=np.random.RandomState(42)
        )
        
        # Extract results
        best_score = -min([trial['result']['loss'] for trial in trials.trials])
        convergence_history = [-trial['result']['loss'] for trial in trials.trials]
        
        return OptimizationResult(
            best_params=best,
            best_score=best_score,
            optimization_time=0.0,
            n_trials=len(trials.trials),
            method=method,
            convergence_history=convergence_history
        )

    def _optimize_with_ray(self, objective_func, param_space, n_trials, method):
        """Optimize using Ray Tune"""
        try:
            # Initialize Ray if not already done
            if not ray.is_initialized():
                ray.init(ignore_reinit_error=True, num_cpus=4, num_gpus=1 if self.gpu_available else 0)

            # Convert parameter space to Ray Tune format
            config = {}
            for name, spec in param_space.items():
                if spec['type'] == 'float':
                    config[name] = tune.uniform(spec['low'], spec['high'])
                elif spec['type'] == 'int':
                    config[name] = tune.randint(spec['low'], spec['high'] + 1)
                elif spec['type'] == 'categorical':
                    config[name] = tune.choice(spec['choices'])

            def trainable(config):
                try:
                    score = objective_func(config)
                    tune.report(score=score)
                except Exception as e:
                    logger.warning(f"Ray Tune objective failed: {e}")
                    tune.report(score=0.0)

            # Select scheduler
            if method == 'ray_tune_asha':
                scheduler = ASHAScheduler(
                    metric="score",
                    mode="max",
                    max_t=n_trials,
                    grace_period=5,
                    reduction_factor=2
                )
            else:  # ray_tune_pbt
                scheduler = PopulationBasedTraining(
                    metric="score",
                    mode="max",
                    perturbation_interval=10,
                    hyperparam_mutations=config
                )

            # Run optimization
            analysis = tune.run(
                trainable,
                config=config,
                num_samples=n_trials,
                scheduler=scheduler,
                resources_per_trial={"cpu": 1, "gpu": 0.25 if self.gpu_available else 0},
                verbose=0
            )

            # Extract results
            best_trial = analysis.get_best_trial("score", "max")
            best_params = best_trial.config
            best_score = best_trial.last_result["score"]

            # Get convergence history
            convergence_history = []
            for trial in analysis.trials:
                if trial.last_result and "score" in trial.last_result:
                    convergence_history.append(trial.last_result["score"])

            return OptimizationResult(
                best_params=best_params,
                best_score=best_score,
                optimization_time=0.0,
                n_trials=len(analysis.trials),
                method=method,
                convergence_history=convergence_history
            )

        except Exception as e:
            logger.error(f"Ray Tune optimization failed: {e}")
            # Fallback to random search
            return self._optimize_with_random_search(objective_func, param_space, n_trials, 1)

    def _optimize_with_random_search(self, objective_func, param_space, n_trials, parallel_jobs):
        """Fallback random search optimization"""
        logger.info("🎲 Using random search optimization")

        best_score = -float('inf')
        best_params = {}
        convergence_history = []

        def evaluate_params(trial_idx):
            # Generate random parameters
            params = {}
            for name, spec in param_space.items():
                if spec['type'] == 'float':
                    params[name] = np.random.uniform(spec['low'], spec['high'])
                elif spec['type'] == 'int':
                    params[name] = np.random.randint(spec['low'], spec['high'] + 1)
                elif spec['type'] == 'categorical':
                    params[name] = np.random.choice(spec['choices'])

            try:
                score = objective_func(params)
                return params, score
            except Exception as e:
                logger.warning(f"Random search evaluation failed: {e}")
                return params, 0.0

        # Run parallel evaluation if requested
        if parallel_jobs > 1:
            with concurrent.futures.ThreadPoolExecutor(max_workers=parallel_jobs) as executor:
                futures = [executor.submit(evaluate_params, i) for i in range(n_trials)]

                for future in concurrent.futures.as_completed(futures):
                    params, score = future.result()
                    convergence_history.append(score)

                    if score > best_score:
                        best_score = score
                        best_params = params
        else:
            # Sequential evaluation
            for i in range(n_trials):
                params, score = evaluate_params(i)
                convergence_history.append(score)

                if score > best_score:
                    best_score = score
                    best_params = params

        return OptimizationResult(
            best_params=best_params,
            best_score=best_score,
            optimization_time=0.0,
            n_trials=n_trials,
            method='random_search',
            convergence_history=convergence_history
        )

    def save_optimization_result(self, result: OptimizationResult, filepath: str):
        """Save optimization result to file"""
        try:
            result_dict = {
                'best_params': result.best_params,
                'best_score': result.best_score,
                'optimization_time': result.optimization_time,
                'n_trials': result.n_trials,
                'method': result.method,
                'convergence_history': result.convergence_history
            }

            with open(filepath, 'w') as f:
                json.dump(result_dict, f, indent=2)

            logger.info(f"💾 Optimization result saved to {filepath}")

        except Exception as e:
            logger.error(f"Failed to save optimization result: {e}")

    def load_optimization_result(self, filepath: str) -> Optional[OptimizationResult]:
        """Load optimization result from file"""
        try:
            with open(filepath, 'r') as f:
                result_dict = json.load(f)

            return OptimizationResult(**result_dict)

        except Exception as e:
            logger.error(f"Failed to load optimization result: {e}")
            return None

    def get_optimization_summary(self) -> Dict[str, Any]:
        """Get summary of available optimization methods"""
        return {
            'gpu_available': self.gpu_available,
            'available_optimizers': self.optimizers,
            'recommended_method': self._select_best_method(50, 5),
            'libraries': {
                'scikit-optimize': SKOPT_AVAILABLE,
                'hyperopt': HYPEROPT_AVAILABLE,
                'ray_tune': RAY_AVAILABLE
            }
        }

# Global instance
gpu_hyperopt_free = GPUHyperoptFree()

# Compatibility function for existing code
def optimize_strategy_parameters_free(objective_func, param_space, n_trials=50, method='auto'):
    """Compatibility function for existing code"""
    return gpu_hyperopt_free.optimize_strategy_parameters(
        objective_func=objective_func,
        param_space=param_space,
        n_trials=n_trials,
        method=method
    )
