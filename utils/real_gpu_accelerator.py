#!/usr/bin/env python3
"""
Real GPU Accelerator for Strategy Evolution
Uses CuPy, PyTorch CUDA, and Numba CUDA for actual GPU processing
"""

import numpy as np
import cupy as cp
import torch
from numba import cuda
import logging
from typing import Dict, List, Any, Tuple
import time

logger = logging.getLogger(__name__)

class RealGPUAccelerator:
    """Real GPU acceleration using CuPy and PyTorch CUDA"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.cuda_available = torch.cuda.is_available()
        
        if self.cuda_available:
            # Setup GPU memory pool
            mempool = cp.get_default_memory_pool()
            mempool.set_limit(size=4 * 1024**3)  # 4GB limit
            
            # Warm up GPU
            self._warmup_gpu()
            logger.info(f"🚀 Real GPU Accelerator initialized on {torch.cuda.get_device_name(0)}")
        else:
            logger.warning("CUDA not available - using CPU fallback")
    
    def _warmup_gpu(self):
        """Warm up GPU with dummy operations"""
        try:
            # CuPy warmup
            x = cp.random.random((1000, 1000), dtype=cp.float32)
            y = cp.matmul(x, x)
            cp.cuda.Stream.null.synchronize()
            
            # PyTorch warmup
            x_torch = torch.randn(1000, 1000, device=self.device, dtype=torch.float32)
            y_torch = torch.matmul(x_torch, x_torch)
            torch.cuda.synchronize()
            
            logger.info("GPU warmed up successfully")
        except Exception as e:
            logger.warning(f"GPU warmup failed: {e}")
    
    def process_strategies_parallel_gpu(self, data_arrays: Dict[str, np.ndarray], 
                                      strategies: List[Dict[str, Any]]) -> Dict[str, np.ndarray]:
        """Process multiple strategies in parallel on GPU"""
        if not self.cuda_available:
            return {}
        
        try:
            start_time = time.time()
            
            # Convert data to GPU arrays
            gpu_data = {}
            for key, array in data_arrays.items():
                gpu_data[key] = cp.asarray(array, dtype=cp.float32)
            
            n_rows = len(gpu_data['close'])
            n_strategies = len(strategies)
            
            # Allocate GPU memory for results
            results = cp.zeros((n_strategies, n_rows), dtype=cp.int8)
            
            # Process strategies in parallel using CUDA kernels
            self._launch_strategy_kernels(gpu_data, strategies, results)
            
            # Convert results back to CPU
            cpu_results = {}
            for i, strategy in enumerate(strategies):
                strategy_name = strategy.get('name', f'Strategy_{i}')
                cpu_results[strategy_name] = cp.asnumpy(results[i])
            
            processing_time = time.time() - start_time
            logger.info(f"🚀 GPU processed {n_strategies} strategies on {n_rows} rows in {processing_time:.3f}s")
            
            return cpu_results
            
        except Exception as e:
            logger.error(f"GPU parallel processing failed: {e}")
            return {}
    
    def _launch_strategy_kernels(self, gpu_data: Dict[str, cp.ndarray], 
                               strategies: List[Dict[str, Any]], 
                               results: cp.ndarray):
        """Launch CUDA kernels for strategy processing"""
        try:
            close = gpu_data['close']
            high = gpu_data.get('high', close)
            low = gpu_data.get('low', close)
            volume = gpu_data.get('volume', cp.ones_like(close))
            
            n_rows = len(close)
            n_strategies = len(strategies)
            
            # Configure CUDA kernel for better GPU utilization
            threads_per_block = 512  # Increase threads per block
            blocks_per_grid = min(2048, (n_rows + threads_per_block - 1) // threads_per_block)  # Limit blocks
            
            # Launch kernel for each strategy type
            for i, strategy in enumerate(strategies):
                strategy_type = strategy.get('name', 'RSI_Reversal')
                
                if 'RSI' in strategy_type:
                    self._launch_rsi_kernel(close, results[i], blocks_per_grid, threads_per_block, n_rows)
                elif 'EMA' in strategy_type:
                    self._launch_ema_kernel(close, results[i], blocks_per_grid, threads_per_block, n_rows)
                else:
                    self._launch_momentum_kernel(close, results[i], blocks_per_grid, threads_per_block, n_rows)
            
            # Synchronize GPU
            cp.cuda.Stream.null.synchronize()
            
        except Exception as e:
            logger.error(f"CUDA kernel launch failed: {e}")
    
    def _launch_rsi_kernel(self, close, signals, blocks_per_grid, threads_per_block, n_rows):
        """Launch RSI strategy kernel"""
        try:
            rsi_strategy_kernel[blocks_per_grid, threads_per_block](
                close, signals, 30.0, 70.0, n_rows
            )
        except Exception as e:
            logger.error(f"RSI kernel failed: {e}")
    
    def _launch_ema_kernel(self, close, signals, blocks_per_grid, threads_per_block, n_rows):
        """Launch EMA crossover kernel"""
        try:
            ema_strategy_kernel[blocks_per_grid, threads_per_block](
                close, signals, 10, 20, n_rows
            )
        except Exception as e:
            logger.error(f"EMA kernel failed: {e}")
    
    def _launch_momentum_kernel(self, close, signals, blocks_per_grid, threads_per_block, n_rows):
        """Launch momentum strategy kernel"""
        try:
            momentum_strategy_kernel[blocks_per_grid, threads_per_block](
                close, signals, 10, 0.02, n_rows
            )
        except Exception as e:
            logger.error(f"Momentum kernel failed: {e}")
    
    def vectorized_backtest_gpu(self, data_arrays: Dict[str, np.ndarray], 
                              strategies: List[Dict[str, Any]]) -> List[Dict[str, float]]:
        """Run vectorized backtesting on GPU"""
        if not self.cuda_available:
            return []
        
        try:
            start_time = time.time()
            
            # Get strategy signals using GPU
            signal_results = self.process_strategies_parallel_gpu(data_arrays, strategies)
            
            if not signal_results:
                return []
            
            # Convert to GPU for backtesting
            close_gpu = cp.asarray(data_arrays['close'], dtype=cp.float32)
            
            results = []
            for strategy_name, signals in signal_results.items():
                # Run GPU backtesting
                metrics = self._gpu_backtest_single_strategy(close_gpu, cp.asarray(signals))
                metrics['strategy_name'] = strategy_name
                results.append(metrics)
            
            processing_time = time.time() - start_time
            logger.info(f"🚀 GPU vectorized backtest completed in {processing_time:.3f}s")
            
            return results
            
        except Exception as e:
            logger.error(f"GPU vectorized backtest failed: {e}")
            return []
    
    def _gpu_backtest_single_strategy(self, close_gpu: cp.ndarray, signals_gpu: cp.ndarray) -> Dict[str, float]:
        """Run backtesting for single strategy on GPU"""
        try:
            n_rows = len(close_gpu)
            
            # Allocate GPU arrays for backtesting
            trades_gpu = cp.zeros(n_rows, dtype=cp.float32)
            equity_curve = cp.zeros(n_rows, dtype=cp.float32)
            
            # Configure kernel for better GPU utilization
            threads_per_block = 512
            blocks_per_grid = min(2048, (n_rows + threads_per_block - 1) // threads_per_block)
            
            # Launch backtesting kernel
            backtest_kernel[blocks_per_grid, threads_per_block](
                close_gpu, signals_gpu, trades_gpu, equity_curve, 0.02, 0.04, n_rows
            )
            
            # Calculate metrics on GPU
            total_trades = int(cp.sum(cp.abs(signals_gpu)))
            winning_trades = int(cp.sum(trades_gpu > 0))
            total_pnl = float(cp.sum(trades_gpu))
            
            # Calculate additional metrics
            win_rate = winning_trades / max(total_trades, 1)
            
            # Calculate Sharpe ratio (simplified)
            returns = cp.diff(equity_curve)
            returns = returns[returns != 0]  # Remove zero returns
            
            if len(returns) > 1:
                sharpe_ratio = float(cp.mean(returns) / cp.std(returns) * cp.sqrt(252))
            else:
                sharpe_ratio = 0.0
            
            # Calculate max drawdown (manual implementation)
            running_max = cp.zeros_like(equity_curve)
            running_max[0] = equity_curve[0]
            for i in range(1, len(equity_curve)):
                running_max[i] = cp.maximum(running_max[i-1], equity_curve[i])
            
            drawdown = (running_max - equity_curve) / cp.maximum(running_max, 1e-8)
            max_drawdown = float(cp.max(drawdown)) * 100
            
            return {
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'total_pnl': total_pnl,
                'roi': total_pnl,
                'win_rate': win_rate,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown
            }
            
        except Exception as e:
            logger.error(f"GPU single strategy backtest failed: {e}")
            return {
                'total_trades': 0, 'winning_trades': 0, 'total_pnl': 0.0,
                'roi': 0.0, 'win_rate': 0.0, 'sharpe_ratio': 0.0, 'max_drawdown': 0.0
            }
    
    def cleanup_gpu_memory(self):
        """Clean up GPU memory"""
        try:
            cp.get_default_memory_pool().free_all_blocks()
            torch.cuda.empty_cache()
            logger.debug("GPU memory cleaned up")
        except Exception as e:
            logger.warning(f"GPU cleanup failed: {e}")

# CUDA Kernels
@cuda.jit
def rsi_strategy_kernel(close, signals, oversold, overbought, n):
    """RSI strategy CUDA kernel"""
    idx = cuda.grid(1)
    
    if idx >= 14 and idx < n:
        # Calculate RSI
        gains = 0.0
        losses = 0.0
        
        for i in range(14):
            diff = close[idx - i] - close[idx - i - 1]
            if diff > 0:
                gains += diff
            else:
                losses -= diff
        
        avg_gain = gains / 14.0
        avg_loss = losses / 14.0
        
        if avg_loss > 0:
            rs = avg_gain / avg_loss
            rsi = 100.0 - (100.0 / (1.0 + rs))
            
            # Generate signals
            if rsi < oversold:
                signals[idx] = 1  # Buy
            elif rsi > overbought:
                signals[idx] = -1  # Sell
            else:
                signals[idx] = 0  # Hold

@cuda.jit
def ema_strategy_kernel(close, signals, fast_period, slow_period, n):
    """EMA crossover strategy CUDA kernel"""
    idx = cuda.grid(1)
    
    if idx >= slow_period and idx < n:
        # Calculate EMAs
        alpha_fast = 2.0 / (fast_period + 1.0)
        alpha_slow = 2.0 / (slow_period + 1.0)
        
        ema_fast = close[idx - fast_period]
        ema_slow = close[idx - slow_period]
        
        for i in range(1, fast_period):
            ema_fast = alpha_fast * close[idx - fast_period + i] + (1 - alpha_fast) * ema_fast
        
        for i in range(1, slow_period):
            ema_slow = alpha_slow * close[idx - slow_period + i] + (1 - alpha_slow) * ema_slow
        
        # Generate signals
        if ema_fast > ema_slow and close[idx - 1] <= close[idx - 2]:
            signals[idx] = 1  # Buy
        elif ema_fast < ema_slow and close[idx - 1] >= close[idx - 2]:
            signals[idx] = -1  # Sell
        else:
            signals[idx] = 0  # Hold

@cuda.jit
def momentum_strategy_kernel(close, signals, lookback, threshold, n):
    """Momentum strategy CUDA kernel"""
    idx = cuda.grid(1)
    
    if idx >= lookback and idx < n:
        momentum = (close[idx] - close[idx - lookback]) / close[idx - lookback]
        
        if momentum > threshold:
            signals[idx] = 1  # Buy
        elif momentum < -threshold:
            signals[idx] = -1  # Sell
        else:
            signals[idx] = 0  # Hold

@cuda.jit
def backtest_kernel(close, signals, trades, equity, stop_loss_pct, take_profit_pct, n):
    """Backtesting CUDA kernel"""
    idx = cuda.grid(1)
    
    if idx < n and signals[idx] != 0:
        entry_price = close[idx]
        signal_type = signals[idx]
        
        # Look for exit in next bars
        for i in range(1, min(50, n - idx)):
            exit_idx = idx + i
            current_price = close[exit_idx]
            
            if signal_type == 1:  # Long position
                if current_price <= entry_price * (1.0 - stop_loss_pct):
                    # Stop loss
                    trades[idx] = (current_price - entry_price) / entry_price
                    break
                elif current_price >= entry_price * (1.0 + take_profit_pct):
                    # Take profit
                    trades[idx] = (current_price - entry_price) / entry_price
                    break
            else:  # Short position
                if current_price >= entry_price * (1.0 + stop_loss_pct):
                    # Stop loss
                    trades[idx] = (entry_price - current_price) / entry_price
                    break
                elif current_price <= entry_price * (1.0 - take_profit_pct):
                    # Take profit
                    trades[idx] = (entry_price - current_price) / entry_price
                    break
        
        # Update equity curve
        if idx > 0:
            equity[idx] = equity[idx - 1] + trades[idx]
        else:
            equity[idx] = trades[idx]

# Additional utility functions for compatibility with enhanced_backtesting_kimi.py
def get_cuda_optimizer():
    """Get the global CUDA optimizer instance"""
    return real_gpu_accelerator

def optimize_cuda_for_backtesting():
    """Optimize CUDA settings for backtesting operations"""
    try:
        if real_gpu_accelerator.cuda_available:
            # Configure optimal settings for backtesting
            import cupy as cp

            # Set memory pool limit
            mempool = cp.get_default_memory_pool()
            mempool.set_limit(size=6 * 1024**3)  # 6GB limit for backtesting

            # Configure PyTorch for optimal performance
            import torch
            if torch.cuda.is_available():
                torch.backends.cudnn.benchmark = True
                torch.backends.cudnn.deterministic = False

            logger.info("🚀 CUDA optimized for backtesting operations")
            return True
        else:
            logger.warning("CUDA not available for optimization")
            return False

    except Exception as e:
        logger.error(f"CUDA optimization failed: {e}")
        return False

class CUDAOptimizer:
    """CUDA optimizer class for compatibility"""

    def __init__(self, accelerator):
        self.accelerator = accelerator
        self.cuda_available = accelerator.cuda_available

    def should_use_cuda(self, data_size, signal_count=None, trade_count=None):
        """Determine if CUDA should be used based on data size and complexity"""
        if not self.cuda_available:
            return False

        # Use CUDA for larger datasets
        min_size = 1000
        if signal_count is not None:
            min_size = max(min_size, 10)  # Lower threshold for signal processing
        if trade_count is not None:
            min_size = max(min_size, 5)   # Lower threshold for trade processing

        return data_size >= min_size

    def get_memory_info(self):
        """Get GPU memory information"""
        try:
            if self.cuda_available:
                import torch
                device = torch.cuda.current_device()
                total_memory = torch.cuda.get_device_properties(device).total_memory
                allocated_memory = torch.cuda.memory_allocated(device)

                return {
                    'device_name': torch.cuda.get_device_name(device),
                    'memory_total': total_memory / (1024**3),  # GB
                    'memory_allocated': allocated_memory / (1024**3),  # GB
                    'memory_free': (total_memory - allocated_memory) / (1024**3)  # GB
                }
            else:
                return {'device_name': 'CPU', 'memory_total': 0, 'memory_allocated': 0, 'memory_free': 0}
        except Exception as e:
            logger.error(f"Failed to get memory info: {e}")
            return {'device_name': 'Unknown', 'memory_total': 0, 'memory_allocated': 0, 'memory_free': 0}

    def optimize_polars_for_gpu(self):
        """Optimize Polars for GPU usage"""
        try:
            import os
            # Set environment variables for Polars GPU optimization
            os.environ['POLARS_MAX_THREADS'] = '8'
            os.environ['POLARS_STREAMING_CHUNK_SIZE'] = '100000'
            logger.info("📊 Polars optimized for GPU usage")
        except Exception as e:
            logger.warning(f"Polars GPU optimization failed: {e}")

    def get_cuda_kernel_config(self, data_size):
        """Get optimal CUDA kernel configuration"""
        threads_per_block = min(512, data_size)
        blocks_per_grid = min(2048, (data_size + threads_per_block - 1) // threads_per_block)
        return blocks_per_grid, threads_per_block

    def get_optimal_batch_size(self, data_size):
        """Get optimal batch size for GPU processing"""
        if data_size < 10000:
            return data_size
        elif data_size < 100000:
            return 10000
        else:
            return 50000

    def cleanup_memory(self):
        """Clean up GPU memory"""
        self.accelerator.cleanup_gpu_memory()

# Extend the RealGPUAccelerator class with additional methods
def extend_gpu_accelerator():
    """Extend the GPU accelerator with additional methods"""

    # Add CUDA optimizer as an attribute
    real_gpu_accelerator.cuda_optimizer = CUDAOptimizer(real_gpu_accelerator)

    # Add compatibility methods
    def should_use_cuda(self, data_size, signal_count=None, trade_count=None):
        return self.cuda_optimizer.should_use_cuda(data_size, signal_count, trade_count)

    def get_memory_info(self):
        return self.cuda_optimizer.get_memory_info()

    def optimize_polars_for_gpu(self):
        return self.cuda_optimizer.optimize_polars_for_gpu()

    def get_cuda_kernel_config(self, data_size):
        return self.cuda_optimizer.get_cuda_kernel_config(data_size)

    def get_optimal_batch_size(self, data_size):
        return self.cuda_optimizer.get_optimal_batch_size(data_size)

    def cleanup_memory(self):
        return self.cuda_optimizer.cleanup_memory()

    # Bind methods to the instance
    real_gpu_accelerator.should_use_cuda = should_use_cuda.__get__(real_gpu_accelerator)
    real_gpu_accelerator.get_memory_info = get_memory_info.__get__(real_gpu_accelerator)
    real_gpu_accelerator.optimize_polars_for_gpu = optimize_polars_for_gpu.__get__(real_gpu_accelerator)
    real_gpu_accelerator.get_cuda_kernel_config = get_cuda_kernel_config.__get__(real_gpu_accelerator)
    real_gpu_accelerator.get_optimal_batch_size = get_optimal_batch_size.__get__(real_gpu_accelerator)
    real_gpu_accelerator.cleanup_memory = cleanup_memory.__get__(real_gpu_accelerator)

# Global instance
real_gpu_accelerator = RealGPUAccelerator()

# Extend the accelerator with additional methods
extend_gpu_accelerator()